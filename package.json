{"name": "customer-1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@babel/parser": "^7.25.3", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "@canva/app-ui-kit": "^3.8.0", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@cosmjs/encoding": "^0.32.4", "@dnd-kit/core": "^6.2.0", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.1.2", "@headlessui/tailwindcss": "^0.2.1", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^4.1.3", "@monaco-editor/react": "^4.6.0", "@nextui-org/react": "^2.4.6", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.19.3", "@react-spring/web": "^9.7.4", "@react-three/drei": "^9.111.4", "@react-three/fiber": "^8.17.6", "@refinedev/react-table": "^5.6.13", "@shopify/polaris": "^13.9.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^5.52.3", "@tanstack/react-table": "^8.20.1", "@tinymce/tinymce-react": "^5.1.1", "@tiptap/extension-collaboration": "^2.6.6", "@tiptap/extension-collaboration-cursor": "^2.6.6", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-placeholder": "^2.6.6", "@tiptap/extension-table": "^2.6.6", "@tiptap/extension-table-cell": "^2.6.6", "@tiptap/extension-table-row": "^2.6.6", "@tiptap/extension-task-item": "^2.6.6", "@tiptap/extension-task-list": "^2.6.6", "@tiptap/react": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "@tiptap/suggestion": "^2.6.6", "@tremor/react": "^3.17.4", "@types/babel__traverse": "^7.20.6", "@types/babel-types": "^7.0.15", "@use-gesture/react": "^10.3.1", "@xyflow/react": "^12.1.0", "ag-grid-react": "^32.1.0", "antd": "^5.20.5", "aos": "^2.3.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "d3-sankey": "^0.12.3", "date-fns": "^3.6.0", "diff": "^6.0.0-beta", "diff-match-patch": "^1.0.5", "echarts-for-react": "^3.0.2", "embla-carousel-react": "^8.5.2", "emoji-picker-react": "^4.11.1", "esbuild": "^0.23.0", "esbuild-plugin-globals": "^0.2.0", "ethers": "^6.13.2", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^11.3.24", "highcharts": "^11.4.7", "highcharts-react-official": "^3.2.1", "input-otp": "^1.4.2", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.3", "leaflet": "^1.9.4", "lucide-react": "^0.477.0", "mapbox-gl": "^1.13.3", "moment": "^2.30.1", "next": "14.2.16", "next-themes": "^0.4.4", "papaparse": "^5.4.1", "plotly": "^1.0.6", "qrcode.react": "^4.2.0", "radix-ui": "^1.3.4", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.13.4", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-colorful": "^5.6.1", "react-confetti": "^6.1.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-feather": "^2.0.10", "react-flow": "^1.0.3", "react-force-graph": "^1.44.4", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-images-uploading": "^3.1.7", "react-intersection-observer": "^9.13.0", "react-konva": "^18.2.10", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.1", "react-masonry-css": "^1.0.16", "react-pdf": "^9.1.0", "react-plotly.js": "^2.6.0", "react-qr-reader-es6": "^2.2.1-2", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.26.2", "react-simple-maps": "^3.0.0", "react-spring": "^9.7.4", "react-syntax-highlighter": "^15.5.0", "react-table": "^7.8.0", "react-type-animation": "^3.2.0", "reactflow": "^11.11.4", "recharts": "^2.15.1", "resend": "^4.5.2", "sonner": "^2.0.1", "sooner": "^1.1.4", "styled-components": "^6.1.13", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.167.1", "tsparticles": "^3.5.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^3.24.2"}, "devDependencies": {"@types/babel__parser": "^7.1.5", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}